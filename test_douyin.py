#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from media_platform.douyin import DouYinCrawler
import config

async def test_douyin_crawler():
    """测试抖音爬虫"""
    print("开始测试抖音爬虫...")
    
    try:
        # 创建爬虫实例
        crawler = DouYinCrawler()
        print("爬虫实例创建成功")
        
        # 启动爬虫
        print("正在启动爬虫...")
        await crawler.start()
        print("爬虫运行完成")
        
    except Exception as e:
        print(f"爬虫运行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("抖音爬虫测试脚本")
    print(f"当前平台配置: {config.PLATFORM}")
    print(f"爬取类型: {config.CRAWLER_TYPE}")
    print(f"视频ID列表: {config.DY_SPECIFIED_ID_LIST}")
    print(f"是否启用媒体下载: {config.ENABLE_GET_MEIDAS}")
    print(f"数据保存方式: {config.SAVE_DATA_OPTION}")
    print("-" * 50)
    
    asyncio.run(test_douyin_crawler())
