# 声明：本代码仅供学习和研究目的使用。使用者应遵守以下原则：
# 1. 不得用于任何商业用途。
# 2. 使用时应遵守目标平台的使用条款和robots.txt规则。
# 3. 不得进行大规模爬取或对平台造成运营干扰。
# 4. 应合理控制请求频率，避免给目标平台带来不必要的负担。
# 5. 不得用于任何非法或不当的用途。
#
# 详细许可条款请参阅项目根目录下的LICENSE文件。
# 使用本代码即表示您同意遵守上述原则和LICENSE中的所有条款。

import re
import httpx
import sys
import os
from typing import Optional
from urllib.parse import urlparse, parse_qs

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from tools import utils
except ImportError:
    # 如果导入失败，创建一个简单的logger
    class SimpleLogger:
        def info(self, msg): print(f"INFO: {msg}")
        def error(self, msg): print(f"ERROR: {msg}")

    class Utils:
        logger = SimpleLogger()

    utils = Utils()


async def extract_douyin_video_id(share_url: str) -> Optional[str]:
    """
    从抖音分享链接中提取视频ID
    
    Args:
        share_url: 抖音分享链接，如 https://v.douyin.com/8kcIG-kP6Xc/
        
    Returns:
        视频ID字符串，如果提取失败则返回None
    """
    try:
        # 处理短链接，需要跟随重定向获取真实URL
        async with httpx.AsyncClient(follow_redirects=True, timeout=10) as client:
            response = await client.head(share_url)
            real_url = str(response.url)
            
        utils.logger.info(f"[extract_douyin_video_id] 短链接重定向到: {real_url}")
        
        # 从真实URL中提取视频ID
        # 抖音视频URL格式通常为: https://www.douyin.com/video/7280854932641664319
        video_id_match = re.search(r'/video/(\d+)', real_url)
        if video_id_match:
            video_id = video_id_match.group(1)
            utils.logger.info(f"[extract_douyin_video_id] 提取到视频ID: {video_id}")
            return video_id
            
        # 尝试从URL参数中提取
        parsed_url = urlparse(real_url)
        params = parse_qs(parsed_url.query)
        
        # 检查常见的参数名
        for param_name in ['aweme_id', 'video_id', 'id']:
            if param_name in params:
                video_id = params[param_name][0]
                utils.logger.info(f"[extract_douyin_video_id] 从参数{param_name}提取到视频ID: {video_id}")
                return video_id
                
        # 尝试从路径中提取数字ID
        path_match = re.search(r'/(\d{15,})', real_url)
        if path_match:
            video_id = path_match.group(1)
            utils.logger.info(f"[extract_douyin_video_id] 从路径提取到视频ID: {video_id}")
            return video_id
            
        utils.logger.error(f"[extract_douyin_video_id] 无法从URL中提取视频ID: {real_url}")
        return None
        
    except Exception as e:
        utils.logger.error(f"[extract_douyin_video_id] 提取视频ID时发生错误: {e}")
        return None


def parse_douyin_share_text(share_text: str) -> Optional[str]:
    """
    从抖音分享文本中提取链接
    
    Args:
        share_text: 包含抖音分享链接的文本
        
    Returns:
        提取到的链接，如果没有找到则返回None
    """
    # 匹配抖音短链接
    url_pattern = r'https://v\.douyin\.com/[A-Za-z0-9\-_]+/?'
    match = re.search(url_pattern, share_text)
    
    if match:
        return match.group(0)
    
    # 也尝试匹配完整的抖音链接
    full_url_pattern = r'https://www\.douyin\.com/video/\d+'
    match = re.search(full_url_pattern, share_text)
    
    if match:
        return match.group(0)
        
    return None


async def get_video_id_from_share_text(share_text: str) -> Optional[str]:
    """
    从抖音分享文本中提取视频ID
    
    Args:
        share_text: 包含抖音分享信息的完整文本
        
    Returns:
        视频ID，如果提取失败则返回None
    """
    # 首先尝试提取链接
    share_url = parse_douyin_share_text(share_text)
    
    if not share_url:
        utils.logger.error("[get_video_id_from_share_text] 未能从分享文本中找到抖音链接")
        return None
        
    utils.logger.info(f"[get_video_id_from_share_text] 找到分享链接: {share_url}")
    
    # 从链接中提取视频ID
    return await extract_douyin_video_id(share_url)


if __name__ == "__main__":
    import asyncio
    
    # 测试用例
    test_share_text = "1.76 01/03 <EMAIL> NjP:/ 用系统思维超越单一目标 # 心理 # 学习 # 知识 # 思维 # 认知  https://v.douyin.com/8kcIG-kP6Xc/ 复制此链接，打开Dou音搜索，直接观看视频！"
    
    async def test():
        video_id = await get_video_id_from_share_text(test_share_text)
        print(f"提取到的视频ID: {video_id}")
    
    asyncio.run(test())
