import requests
import re
from urllib.parse import urlparse, parse_qs

def extract_douyin_video_id(share_url):
    """
    从抖音分享链接中提取视频ID
    """
    try:
        print(f"正在处理链接: {share_url}")

        # 跟随重定向获取真实URL
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(share_url, headers=headers, allow_redirects=True, timeout=10)
        real_url = response.url

        print(f"短链接重定向到: {real_url}")

        # 从真实URL中提取视频ID
        video_id_match = re.search(r'/video/(\d+)', real_url)
        if video_id_match:
            video_id = video_id_match.group(1)
            print(f"提取到视频ID: {video_id}")
            return video_id

        # 尝试从路径中提取数字ID
        path_match = re.search(r'/(\d{15,})', real_url)
        if path_match:
            video_id = path_match.group(1)
            print(f"从路径提取到视频ID: {video_id}")
            return video_id

        # 尝试从页面内容中提取
        if 'douyin.com' in real_url:
            content = response.text
            # 查找页面中的视频ID
            id_patterns = [
                r'"aweme_id":"(\d+)"',
                r'"video_id":"(\d+)"',
                r'aweme/(\d+)',
                r'video/(\d+)'
            ]

            for pattern in id_patterns:
                match = re.search(pattern, content)
                if match:
                    video_id = match.group(1)
                    print(f"从页面内容提取到视频ID: {video_id}")
                    return video_id

        print(f"无法从URL中提取视频ID: {real_url}")
        return None

    except Exception as e:
        print(f"提取视频ID时发生错误: {e}")
        return None

def parse_douyin_share_text(share_text):
    """
    从抖音分享文本中提取链接
    """
    # 匹配抖音短链接
    url_pattern = r'https://v\.douyin\.com/[A-Za-z0-9\-_]+/?'
    match = re.search(url_pattern, share_text)
    
    if match:
        return match.group(0)
        
    return None

# 测试
if __name__ == "__main__":
    test_share_text = "1.76 01/03 <EMAIL> NjP:/ 用系统思维超越单一目标 # 心理 # 学习 # 知识 # 思维 # 认知  https://v.douyin.com/8kcIG-kP6Xc/ 复制此链接，打开Dou音搜索，直接观看视频！"
    
    # 提取链接
    share_url = parse_douyin_share_text(test_share_text)
    print(f"找到分享链接: {share_url}")
    
    if share_url:
        # 提取视频ID
        video_id = extract_douyin_video_id(share_url)
        print(f"最终提取到的视频ID: {video_id}")
    else:
        print("未能从分享文本中找到抖音链接")
